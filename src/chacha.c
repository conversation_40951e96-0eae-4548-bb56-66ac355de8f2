#include "mercha.h"

#include <omp.h>

#include <immintrin.h> // <--- 包含 SIMD intrinsics 头文件

// AVX512优化的quarter round函数
static inline void avx512_quarter_round_4x(uint32_t *state,
                                           int a0, int b0, int c0, int d0,
                                           int a1, int b1, int c1, int d1,
                                           int a2, int b2, int c2, int d2,
                                           int a3, int b3, int c3, int d3) {
    // 将4个quarter round的参数打包到AVX512寄存器中进行并行计算
    __m512i a = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, state[a3], state[a2], state[a1], state[a0]);
    __m512i b = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, state[b3], state[b2], state[b1], state[b0]);
    __m512i c = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, state[c3], state[c2], state[c1], state[c0]);
    __m512i d = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, state[d3], state[d2], state[d1], state[d0]);

    // Quarter round的四个步骤，并行执行4个quarter round
    // 步骤1: a += b; d ^= a; d = ROTL32(d, 16);
    a = _mm512_add_epi32(a, b);
    d = _mm512_xor_si512(d, a);
    d = _mm512_rol_epi32(d, 16);

    // 步骤2: c += d; b ^= c; b = ROTL32(b, 12);
    c = _mm512_add_epi32(c, d);
    b = _mm512_xor_si512(b, c);
    b = _mm512_rol_epi32(b, 12);

    // 步骤3: a += b; d ^= a; d = ROTL32(d, 8);
    a = _mm512_add_epi32(a, b);
    d = _mm512_xor_si512(d, a);
    d = _mm512_rol_epi32(d, 8);

    // 步骤4: c += d; b ^= c; b = ROTL32(b, 7);
    c = _mm512_add_epi32(c, d);
    b = _mm512_xor_si512(b, c);
    b = _mm512_rol_epi32(b, 7);

    // 将结果存回state数组
    uint32_t temp_a[16], temp_b[16], temp_c[16], temp_d[16];
    _mm512_storeu_si512((__m512i*)temp_a, a);
    _mm512_storeu_si512((__m512i*)temp_b, b);
    _mm512_storeu_si512((__m512i*)temp_c, c);
    _mm512_storeu_si512((__m512i*)temp_d, d);

    state[a0] = temp_a[0]; state[a1] = temp_a[1]; state[a2] = temp_a[2]; state[a3] = temp_a[3];
    state[b0] = temp_b[0]; state[b1] = temp_b[1]; state[b2] = temp_b[2]; state[b3] = temp_b[3];
    state[c0] = temp_c[0]; state[c1] = temp_c[1]; state[c2] = temp_c[2]; state[c3] = temp_c[3];
    state[d0] = temp_d[0]; state[d1] = temp_d[1]; state[d2] = temp_d[2]; state[d3] = temp_d[3];
}

// AVX512优化的diagonal quarter round函数
static inline void avx512_diagonal_quarter_round_4x(uint32_t *state,
                                                    int a0, int b0, int c0, int d0,
                                                    int a1, int b1, int c1, int d1,
                                                    int a2, int b2, int c2, int d2,
                                                    int a3, int b3, int c3, int d3) {
    // 与上面的函数相同，但用于对角线quarter round
    __m512i a = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, state[a3], state[a2], state[a1], state[a0]);
    __m512i b = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, state[b3], state[b2], state[b1], state[b0]);
    __m512i c = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, state[c3], state[c2], state[c1], state[c0]);
    __m512i d = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, state[d3], state[d2], state[d1], state[d0]);

    a = _mm512_add_epi32(a, b);
    d = _mm512_xor_si512(d, a);
    d = _mm512_rol_epi32(d, 16);

    c = _mm512_add_epi32(c, d);
    b = _mm512_xor_si512(b, c);
    b = _mm512_rol_epi32(b, 12);

    a = _mm512_add_epi32(a, b);
    d = _mm512_xor_si512(d, a);
    d = _mm512_rol_epi32(d, 8);

    c = _mm512_add_epi32(c, d);
    b = _mm512_xor_si512(b, c);
    b = _mm512_rol_epi32(b, 7);

    uint32_t temp_a[16], temp_b[16], temp_c[16], temp_d[16];
    _mm512_storeu_si512((__m512i*)temp_a, a);
    _mm512_storeu_si512((__m512i*)temp_b, b);
    _mm512_storeu_si512((__m512i*)temp_c, c);
    _mm512_storeu_si512((__m512i*)temp_d, d);

    state[a0] = temp_a[0]; state[a1] = temp_a[1]; state[a2] = temp_a[2]; state[a3] = temp_a[3];
    state[b0] = temp_b[0]; state[b1] = temp_b[1]; state[b2] = temp_b[2]; state[b3] = temp_b[3];
    state[c0] = temp_c[0]; state[c1] = temp_c[1]; state[c2] = temp_c[2]; state[c3] = temp_c[3];
    state[d0] = temp_d[0]; state[d1] = temp_d[1]; state[d2] = temp_d[2]; state[d3] = temp_d[3];
}

static void chacha20_block(uint32_t state[16], uint8_t out[64]) {
    // AVX512优化版本的ChaCha20块函数
    // 使用AVX512指令集并行化quarter round计算，同时保持循环展开结构

    uint32_t working_state[16] __attribute__((aligned(64)));
    memcpy(working_state, state, 64);

    // Loop unrolling: 展开10次迭代，每次迭代包含8个quarter_round调用
    // 使用AVX512优化的quarter round函数进行并行计算

    // Round 1
    // 第一组并行quarter rounds: (0,4,8,12), (1,5,9,13), (2,6,10,14), (3,7,11,15)
    avx512_quarter_round_4x(working_state, 0, 4, 8, 12, 1, 5, 9, 13, 2, 6, 10, 14, 3, 7, 11, 15);

    // 第二组并行quarter rounds: (0,5,10,15), (1,6,11,12), (2,7,8,13), (3,4,9,14)
    avx512_diagonal_quarter_round_4x(working_state, 0, 5, 10, 15, 1, 6, 11, 12, 2, 7, 8, 13, 3, 4, 9, 14);

    // Round 2 - 使用AVX512优化的quarter round操作
    // 第一组并行quarter rounds: (0,4,8,12), (1,5,9,13), (2,6,10,14), (3,7,11,15)
    a1 = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, working_state_array[3], working_state_array[2], working_state_array[1], working_state_array[0]);
    b1 = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, working_state_array[7], working_state_array[6], working_state_array[5], working_state_array[4]);
    c1 = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, working_state_array[11], working_state_array[10], working_state_array[9], working_state_array[8]);
    d1 = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, working_state_array[15], working_state_array[14], working_state_array[13], working_state_array[12]);

    a1 = _mm512_add_epi32(a1, b1);
    d1 = _mm512_xor_si512(d1, a1);
    d1 = _mm512_rol_epi32(d1, 16);

    c1 = _mm512_add_epi32(c1, d1);
    b1 = _mm512_xor_si512(b1, c1);
    b1 = _mm512_rol_epi32(b1, 12);

    a1 = _mm512_add_epi32(a1, b1);
    d1 = _mm512_xor_si512(d1, a1);
    d1 = _mm512_rol_epi32(d1, 8);

    c1 = _mm512_add_epi32(c1, d1);
    b1 = _mm512_xor_si512(b1, c1);
    b1 = _mm512_rol_epi32(b1, 7);

    _mm512_storeu_si512((__m512i*)&working_state_array[0], a1);
    _mm512_storeu_si512((__m512i*)&working_state_array[4], b1);
    _mm512_storeu_si512((__m512i*)&working_state_array[8], c1);
    _mm512_storeu_si512((__m512i*)&working_state_array[12], d1);

    // 第二组并行quarter rounds: (0,5,10,15), (1,6,11,12), (2,7,8,13), (3,4,9,14)
    a2 = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, working_state_array[3], working_state_array[2], working_state_array[1], working_state_array[0]);
    b2 = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, working_state_array[4], working_state_array[7], working_state_array[6], working_state_array[5]);
    c2 = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, working_state_array[9], working_state_array[8], working_state_array[11], working_state_array[10]);
    d2 = _mm512_set_epi32(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, working_state_array[14], working_state_array[13], working_state_array[12], working_state_array[15]);

    a2 = _mm512_add_epi32(a2, b2);
    d2 = _mm512_xor_si512(d2, a2);
    d2 = _mm512_rol_epi32(d2, 16);

    c2 = _mm512_add_epi32(c2, d2);
    b2 = _mm512_xor_si512(b2, c2);
    b2 = _mm512_rol_epi32(b2, 12);

    a2 = _mm512_add_epi32(a2, b2);
    d2 = _mm512_xor_si512(d2, a2);
    d2 = _mm512_rol_epi32(d2, 8);

    c2 = _mm512_add_epi32(c2, d2);
    b2 = _mm512_xor_si512(b2, c2);
    b2 = _mm512_rol_epi32(b2, 7);

    _mm512_storeu_si512((__m512i*)temp_a2, a2);
    _mm512_storeu_si512((__m512i*)temp_b2, b2);
    _mm512_storeu_si512((__m512i*)temp_c2, c2);
    _mm512_storeu_si512((__m512i*)temp_d2, d2);

    working_state_array[0] = temp_a2[0]; working_state_array[1] = temp_a2[1]; working_state_array[2] = temp_a2[2]; working_state_array[3] = temp_a2[3];
    working_state_array[5] = temp_b2[0]; working_state_array[6] = temp_b2[1]; working_state_array[7] = temp_b2[2]; working_state_array[4] = temp_b2[3];
    working_state_array[10] = temp_c2[0]; working_state_array[11] = temp_c2[1]; working_state_array[8] = temp_c2[2]; working_state_array[9] = temp_c2[3];
    working_state_array[15] = temp_d2[0]; working_state_array[12] = temp_d2[1]; working_state_array[13] = temp_d2[2]; working_state_array[14] = temp_d2[3];

    // Round 3
    // chacha_quarter_round(working_state, 0, 4, 8, 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 16);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 8);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 1, 5, 9, 13);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 16);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 12);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 8);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 7);

    // chacha_quarter_round(working_state, 2, 6, 10, 14);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 16);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 12);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 8);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 3, 7, 11, 15);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 16);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 12);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 8);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 0, 5, 10, 15);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 16);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 12);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 8);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 1, 6, 11, 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 16);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 8);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 2, 7, 8, 13);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 16);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 12);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 8);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 3, 4, 9, 14);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 16);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 12);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 8);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 7);

    // Round 4
    // chacha_quarter_round(working_state, 0, 4, 8, 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 16);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 8);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 1, 5, 9, 13);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 16);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 12);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 8);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 7);

    // chacha_quarter_round(working_state, 2, 6, 10, 14);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 16);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 12);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 8);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 3, 7, 11, 15);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 16);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 12);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 8);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 0, 5, 10, 15);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 16);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 12);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 8);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 1, 6, 11, 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 16);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 8);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 2, 7, 8, 13);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 16);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 12);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 8);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 3, 4, 9, 14);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 16);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 12);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 8);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 7);

    // Round 5
    // chacha_quarter_round(working_state, 0, 4, 8, 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 16);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 8);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 1, 5, 9, 13);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 16);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 12);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 8);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 7);

    // chacha_quarter_round(working_state, 2, 6, 10, 14);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 16);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 12);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 8);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 3, 7, 11, 15);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 16);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 12);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 8);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 0, 5, 10, 15);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 16);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 12);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 8);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 1, 6, 11, 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 16);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 8);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 2, 7, 8, 13);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 16);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 12);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 8);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 3, 4, 9, 14);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 16);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 12);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 8);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 7);

    // Round 6
    // chacha_quarter_round(working_state, 0, 4, 8, 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 16);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 8);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 1, 5, 9, 13);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 16);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 12);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 8);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 7);

    // chacha_quarter_round(working_state, 2, 6, 10, 14);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 16);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 12);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 8);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 3, 7, 11, 15);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 16);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 12);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 8);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 0, 5, 10, 15);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 16);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 12);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 8);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 1, 6, 11, 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 16);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 8);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 2, 7, 8, 13);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 16);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 12);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 8);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 3, 4, 9, 14);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 16);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 12);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 8);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 7);

    // Round 7
    // chacha_quarter_round(working_state, 0, 4, 8, 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 16);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 8);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 1, 5, 9, 13);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 16);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 12);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 8);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 7);

    // chacha_quarter_round(working_state, 2, 6, 10, 14);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 16);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 12);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 8);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 3, 7, 11, 15);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 16);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 12);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 8);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 0, 5, 10, 15);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 16);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 12);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 8);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 1, 6, 11, 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 16);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 8);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 2, 7, 8, 13);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 16);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 12);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 8);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 3, 4, 9, 14);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 16);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 12);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 8);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 7);

    // Round 8
    // chacha_quarter_round(working_state, 0, 4, 8, 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 16);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 8);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 1, 5, 9, 13);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 16);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 12);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 8);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 7);

    // chacha_quarter_round(working_state, 2, 6, 10, 14);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 16);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 12);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 8);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 3, 7, 11, 15);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 16);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 12);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 8);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 0, 5, 10, 15);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 16);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 12);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 8);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 1, 6, 11, 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 16);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 8);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 2, 7, 8, 13);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 16);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 12);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 8);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 3, 4, 9, 14);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 16);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 12);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 8);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 7);

    // Round 9
    // chacha_quarter_round(working_state, 0, 4, 8, 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 16);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 8);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 1, 5, 9, 13);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 16);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 12);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 8);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 7);

    // chacha_quarter_round(working_state, 2, 6, 10, 14);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 16);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 12);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 8);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 3, 7, 11, 15);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 16);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 12);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 8);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 0, 5, 10, 15);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 16);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 12);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 8);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 1, 6, 11, 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 16);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 8);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 2, 7, 8, 13);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 16);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 12);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 8);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 3, 4, 9, 14);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 16);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 12);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 8);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 7);

    // Round 10 (最后一轮)
    // chacha_quarter_round(working_state, 0, 4, 8, 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 16);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 12);
    working_state[0] = working_state[0] + working_state[4]; working_state[12] = ROTL32(working_state[12] ^ working_state[0], 8);
    working_state[8] = working_state[8] + working_state[12]; working_state[4] = ROTL32(working_state[4] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 1, 5, 9, 13);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 16);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 12);
    working_state[1] = working_state[1] + working_state[5]; working_state[13] = ROTL32(working_state[13] ^ working_state[1], 8);
    working_state[9] = working_state[9] + working_state[13]; working_state[5] = ROTL32(working_state[5] ^ working_state[9], 7);

    // chacha_quarter_round(working_state, 2, 6, 10, 14);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 16);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 12);
    working_state[2] = working_state[2] + working_state[6]; working_state[14] = ROTL32(working_state[14] ^ working_state[2], 8);
    working_state[10] = working_state[10] + working_state[14]; working_state[6] = ROTL32(working_state[6] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 3, 7, 11, 15);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 16);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 12);
    working_state[3] = working_state[3] + working_state[7]; working_state[15] = ROTL32(working_state[15] ^ working_state[3], 8);
    working_state[11] = working_state[11] + working_state[15]; working_state[7] = ROTL32(working_state[7] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 0, 5, 10, 15);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 16);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 12);
    working_state[0] = working_state[0] + working_state[5]; working_state[15] = ROTL32(working_state[15] ^ working_state[0], 8);
    working_state[10] = working_state[10] + working_state[15]; working_state[5] = ROTL32(working_state[5] ^ working_state[10], 7);

    // chacha_quarter_round(working_state, 1, 6, 11, 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 16);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 12);
    working_state[1] = working_state[1] + working_state[6]; working_state[12] = ROTL32(working_state[12] ^ working_state[1], 8);
    working_state[11] = working_state[11] + working_state[12]; working_state[6] = ROTL32(working_state[6] ^ working_state[11], 7);

    // chacha_quarter_round(working_state, 2, 7, 8, 13);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 16);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 12);
    working_state[2] = working_state[2] + working_state[7]; working_state[13] = ROTL32(working_state[13] ^ working_state[2], 8);
    working_state[8] = working_state[8] + working_state[13]; working_state[7] = ROTL32(working_state[7] ^ working_state[8], 7);

    // chacha_quarter_round(working_state, 3, 4, 9, 14);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 16);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 12);
    working_state[3] = working_state[3] + working_state[4]; working_state[14] = ROTL32(working_state[14] ^ working_state[3], 8);
    working_state[9] = working_state[9] + working_state[14]; working_state[4] = ROTL32(working_state[4] ^ working_state[9], 7);

    // 最后的状态加法 - 展开循环
    working_state[0] += state[0];
    working_state[1] += state[1];
    working_state[2] += state[2];
    working_state[3] += state[3];
    working_state[4] += state[4];
    working_state[5] += state[5];
    working_state[6] += state[6];
    working_state[7] += state[7];
    working_state[8] += state[8];
    working_state[9] += state[9];
    working_state[10] += state[10];
    working_state[11] += state[11];
    working_state[12] += state[12];
    working_state[13] += state[13];
    working_state[14] += state[14];
    working_state[15] += state[15];

    // 输出转换 - 展开循环
    out[0] = (uint8_t)(working_state[0] >> 0);
    out[1] = (uint8_t)(working_state[0] >> 8);
    out[2] = (uint8_t)(working_state[0] >> 16);
    out[3] = (uint8_t)(working_state[0] >> 24);

    out[4] = (uint8_t)(working_state[1] >> 0);
    out[5] = (uint8_t)(working_state[1] >> 8);
    out[6] = (uint8_t)(working_state[1] >> 16);
    out[7] = (uint8_t)(working_state[1] >> 24);

    out[8] = (uint8_t)(working_state[2] >> 0);
    out[9] = (uint8_t)(working_state[2] >> 8);
    out[10] = (uint8_t)(working_state[2] >> 16);
    out[11] = (uint8_t)(working_state[2] >> 24);

    out[12] = (uint8_t)(working_state[3] >> 0);
    out[13] = (uint8_t)(working_state[3] >> 8);
    out[14] = (uint8_t)(working_state[3] >> 16);
    out[15] = (uint8_t)(working_state[3] >> 24);

    out[16] = (uint8_t)(working_state[4] >> 0);
    out[17] = (uint8_t)(working_state[4] >> 8);
    out[18] = (uint8_t)(working_state[4] >> 16);
    out[19] = (uint8_t)(working_state[4] >> 24);

    out[20] = (uint8_t)(working_state[5] >> 0);
    out[21] = (uint8_t)(working_state[5] >> 8);
    out[22] = (uint8_t)(working_state[5] >> 16);
    out[23] = (uint8_t)(working_state[5] >> 24);

    out[24] = (uint8_t)(working_state[6] >> 0);
    out[25] = (uint8_t)(working_state[6] >> 8);
    out[26] = (uint8_t)(working_state[6] >> 16);
    out[27] = (uint8_t)(working_state[6] >> 24);

    out[28] = (uint8_t)(working_state[7] >> 0);
    out[29] = (uint8_t)(working_state[7] >> 8);
    out[30] = (uint8_t)(working_state[7] >> 16);
    out[31] = (uint8_t)(working_state[7] >> 24);

    out[32] = (uint8_t)(working_state[8] >> 0);
    out[33] = (uint8_t)(working_state[8] >> 8);
    out[34] = (uint8_t)(working_state[8] >> 16);
    out[35] = (uint8_t)(working_state[8] >> 24);

    out[36] = (uint8_t)(working_state[9] >> 0);
    out[37] = (uint8_t)(working_state[9] >> 8);
    out[38] = (uint8_t)(working_state[9] >> 16);
    out[39] = (uint8_t)(working_state[9] >> 24);

    out[40] = (uint8_t)(working_state[10] >> 0);
    out[41] = (uint8_t)(working_state[10] >> 8);
    out[42] = (uint8_t)(working_state[10] >> 16);
    out[43] = (uint8_t)(working_state[10] >> 24);

    out[44] = (uint8_t)(working_state[11] >> 0);
    out[45] = (uint8_t)(working_state[11] >> 8);
    out[46] = (uint8_t)(working_state[11] >> 16);
    out[47] = (uint8_t)(working_state[11] >> 24);

    out[48] = (uint8_t)(working_state[12] >> 0);
    out[49] = (uint8_t)(working_state[12] >> 8);
    out[50] = (uint8_t)(working_state[12] >> 16);
    out[51] = (uint8_t)(working_state[12] >> 24);

    out[52] = (uint8_t)(working_state[13] >> 0);
    out[53] = (uint8_t)(working_state[13] >> 8);
    out[54] = (uint8_t)(working_state[13] >> 16);
    out[55] = (uint8_t)(working_state[13] >> 24);

    out[56] = (uint8_t)(working_state[14] >> 0);
    out[57] = (uint8_t)(working_state[14] >> 8);
    out[58] = (uint8_t)(working_state[14] >> 16);
    out[59] = (uint8_t)(working_state[14] >> 24);

    out[60] = (uint8_t)(working_state[15] >> 0);
    out[61] = (uint8_t)(working_state[15] >> 8);
    out[62] = (uint8_t)(working_state[15] >> 16);
    out[63] = (uint8_t)(working_state[15] >> 24);
}

void chacha20_encrypt(const uint8_t key[32], const uint8_t nonce[12], uint32_t initial_counter, uint8_t *buffer, size_t length) {
    uint32_t key_words[8];
    uint32_t nonce_words[3];

    // Loop unrolling: 展开8次迭代
    key_words[0] = (uint32_t)key[0*4 + 0]      |
                  ((uint32_t)key[0*4 + 1] << 8)  |
                  ((uint32_t)key[0*4 + 2] << 16) |
                  ((uint32_t)key[0*4 + 3] << 24);
    key_words[1] = (uint32_t)key[1*4 + 0]      |
                  ((uint32_t)key[1*4 + 1] << 8)  |
                  ((uint32_t)key[1*4 + 2] << 16) |
                  ((uint32_t)key[1*4 + 3] << 24);
    key_words[2] = (uint32_t)key[2*4 + 0]      |
                  ((uint32_t)key[2*4 + 1] << 8)  |
                  ((uint32_t)key[2*4 + 2] << 16) |
                  ((uint32_t)key[2*4 + 3] << 24);
    key_words[3] = (uint32_t)key[3*4 + 0]      |
                  ((uint32_t)key[3*4 + 1] << 8)  |
                  ((uint32_t)key[3*4 + 2] << 16) |
                  ((uint32_t)key[3*4 + 3] << 24);
    key_words[4] = (uint32_t)key[4*4 + 0]      |
                  ((uint32_t)key[4*4 + 1] << 8)  |
                  ((uint32_t)key[4*4 + 2] << 16) |
                  ((uint32_t)key[4*4 + 3] << 24);
    key_words[5] = (uint32_t)key[5*4 + 0]      |
                  ((uint32_t)key[5*4 + 1] << 8)  |
                  ((uint32_t)key[5*4 + 2] << 16) |
                  ((uint32_t)key[5*4 + 3] << 24);
    key_words[6] = (uint32_t)key[6*4 + 0]      |
                  ((uint32_t)key[6*4 + 1] << 8)  |
                  ((uint32_t)key[6*4 + 2] << 16) |
                  ((uint32_t)key[6*4 + 3] << 24);
    key_words[7] = (uint32_t)key[7*4 + 0]      |
                  ((uint32_t)key[7*4 + 1] << 8)  |
                  ((uint32_t)key[7*4 + 2] << 16) |
                  ((uint32_t)key[7*4 + 3] << 24);

    // Loop unrolling: 展开3次迭代
    nonce_words[0] = (uint32_t)nonce[0*4 + 0]      |
                    ((uint32_t)nonce[0*4 + 1] << 8)  |
                    ((uint32_t)nonce[0*4 + 2] << 16) |
                    ((uint32_t)nonce[0*4 + 3] << 24);
    nonce_words[1] = (uint32_t)nonce[1*4 + 0]      |
                    ((uint32_t)nonce[1*4 + 1] << 8)  |
                    ((uint32_t)nonce[1*4 + 2] << 16) |
                    ((uint32_t)nonce[1*4 + 3] << 24);
    nonce_words[2] = (uint32_t)nonce[2*4 + 0]      |
                    ((uint32_t)nonce[2*4 + 1] << 8)  |
                    ((uint32_t)nonce[2*4 + 2] << 16) |
                    ((uint32_t)nonce[2*4 + 3] << 24);

    // <--- 2. 将原先的循环处理逻辑修改为并行处理
    // 计算总共有多少个64字节的块
    size_t num_blocks = (length + 63) / 64; // 加63是为了向上取整

    #pragma omp parallel for
    for (size_t block_idx = 0; block_idx < num_blocks; ++block_idx) {
        // 每个线程为它处理的块准备独立的 ChaCha 状态
        // 这样可以避免线程间对 state[12] 的竞争，并确保每个块使用正确的计数器
        uint32_t current_block_counter = initial_counter + (uint32_t)block_idx;

        uint32_t state[16] = {
            0x61707865, 0x3320646e, 0x79622d32, 0x6b206574,
            key_words[0], key_words[1], key_words[2], key_words[3],
            key_words[4], key_words[5], key_words[6], key_words[7],
            current_block_counter, // 使用当前块计算得到的计数器
            nonce_words[0], nonce_words[1], nonce_words[2]
        };

        uint8_t key_stream[64];
        chacha20_block(state, key_stream);

        // 计算当前块在 buffer 中的偏移量和实际要处理的字节数
        size_t current_offset = block_idx * 64;
        size_t bytes_to_process = (block_idx == num_blocks - 1) ? (length - current_offset) : 64;
        // 如果是最后一个块，可能不足64字节

        // 优化：对于完整的64字节块，展开循环
        if (bytes_to_process == 64) {
            // Loop unrolling: 展开64次迭代
            buffer[current_offset + 0] = buffer[current_offset + 0] ^ key_stream[0];
            buffer[current_offset + 1] = buffer[current_offset + 1] ^ key_stream[1];
            buffer[current_offset + 2] = buffer[current_offset + 2] ^ key_stream[2];
            buffer[current_offset + 3] = buffer[current_offset + 3] ^ key_stream[3];
            buffer[current_offset + 4] = buffer[current_offset + 4] ^ key_stream[4];
            buffer[current_offset + 5] = buffer[current_offset + 5] ^ key_stream[5];
            buffer[current_offset + 6] = buffer[current_offset + 6] ^ key_stream[6];
            buffer[current_offset + 7] = buffer[current_offset + 7] ^ key_stream[7];
            buffer[current_offset + 8] = buffer[current_offset + 8] ^ key_stream[8];
            buffer[current_offset + 9] = buffer[current_offset + 9] ^ key_stream[9];
            buffer[current_offset + 10] = buffer[current_offset + 10] ^ key_stream[10];
            buffer[current_offset + 11] = buffer[current_offset + 11] ^ key_stream[11];
            buffer[current_offset + 12] = buffer[current_offset + 12] ^ key_stream[12];
            buffer[current_offset + 13] = buffer[current_offset + 13] ^ key_stream[13];
            buffer[current_offset + 14] = buffer[current_offset + 14] ^ key_stream[14];
            buffer[current_offset + 15] = buffer[current_offset + 15] ^ key_stream[15];
            buffer[current_offset + 16] = buffer[current_offset + 16] ^ key_stream[16];
            buffer[current_offset + 17] = buffer[current_offset + 17] ^ key_stream[17];
            buffer[current_offset + 18] = buffer[current_offset + 18] ^ key_stream[18];
            buffer[current_offset + 19] = buffer[current_offset + 19] ^ key_stream[19];
            buffer[current_offset + 20] = buffer[current_offset + 20] ^ key_stream[20];
            buffer[current_offset + 21] = buffer[current_offset + 21] ^ key_stream[21];
            buffer[current_offset + 22] = buffer[current_offset + 22] ^ key_stream[22];
            buffer[current_offset + 23] = buffer[current_offset + 23] ^ key_stream[23];
            buffer[current_offset + 24] = buffer[current_offset + 24] ^ key_stream[24];
            buffer[current_offset + 25] = buffer[current_offset + 25] ^ key_stream[25];
            buffer[current_offset + 26] = buffer[current_offset + 26] ^ key_stream[26];
            buffer[current_offset + 27] = buffer[current_offset + 27] ^ key_stream[27];
            buffer[current_offset + 28] = buffer[current_offset + 28] ^ key_stream[28];
            buffer[current_offset + 29] = buffer[current_offset + 29] ^ key_stream[29];
            buffer[current_offset + 30] = buffer[current_offset + 30] ^ key_stream[30];
            buffer[current_offset + 31] = buffer[current_offset + 31] ^ key_stream[31];
            buffer[current_offset + 32] = buffer[current_offset + 32] ^ key_stream[32];
            buffer[current_offset + 33] = buffer[current_offset + 33] ^ key_stream[33];
            buffer[current_offset + 34] = buffer[current_offset + 34] ^ key_stream[34];
            buffer[current_offset + 35] = buffer[current_offset + 35] ^ key_stream[35];
            buffer[current_offset + 36] = buffer[current_offset + 36] ^ key_stream[36];
            buffer[current_offset + 37] = buffer[current_offset + 37] ^ key_stream[37];
            buffer[current_offset + 38] = buffer[current_offset + 38] ^ key_stream[38];
            buffer[current_offset + 39] = buffer[current_offset + 39] ^ key_stream[39];
            buffer[current_offset + 40] = buffer[current_offset + 40] ^ key_stream[40];
            buffer[current_offset + 41] = buffer[current_offset + 41] ^ key_stream[41];
            buffer[current_offset + 42] = buffer[current_offset + 42] ^ key_stream[42];
            buffer[current_offset + 43] = buffer[current_offset + 43] ^ key_stream[43];
            buffer[current_offset + 44] = buffer[current_offset + 44] ^ key_stream[44];
            buffer[current_offset + 45] = buffer[current_offset + 45] ^ key_stream[45];
            buffer[current_offset + 46] = buffer[current_offset + 46] ^ key_stream[46];
            buffer[current_offset + 47] = buffer[current_offset + 47] ^ key_stream[47];
            buffer[current_offset + 48] = buffer[current_offset + 48] ^ key_stream[48];
            buffer[current_offset + 49] = buffer[current_offset + 49] ^ key_stream[49];
            buffer[current_offset + 50] = buffer[current_offset + 50] ^ key_stream[50];
            buffer[current_offset + 51] = buffer[current_offset + 51] ^ key_stream[51];
            buffer[current_offset + 52] = buffer[current_offset + 52] ^ key_stream[52];
            buffer[current_offset + 53] = buffer[current_offset + 53] ^ key_stream[53];
            buffer[current_offset + 54] = buffer[current_offset + 54] ^ key_stream[54];
            buffer[current_offset + 55] = buffer[current_offset + 55] ^ key_stream[55];
            buffer[current_offset + 56] = buffer[current_offset + 56] ^ key_stream[56];
            buffer[current_offset + 57] = buffer[current_offset + 57] ^ key_stream[57];
            buffer[current_offset + 58] = buffer[current_offset + 58] ^ key_stream[58];
            buffer[current_offset + 59] = buffer[current_offset + 59] ^ key_stream[59];
            buffer[current_offset + 60] = buffer[current_offset + 60] ^ key_stream[60];
            buffer[current_offset + 61] = buffer[current_offset + 61] ^ key_stream[61];
            buffer[current_offset + 62] = buffer[current_offset + 62] ^ key_stream[62];
            buffer[current_offset + 63] = buffer[current_offset + 63] ^ key_stream[63];
        } else {
            // 对于不完整的块，保持原来的循环
            for (size_t i = 0; i < bytes_to_process; i++) {
                buffer[current_offset + i] = buffer[current_offset + i] ^ key_stream[i];
            }
        }
    }
}