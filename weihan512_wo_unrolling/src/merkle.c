#include "mercha.h"
#include <omp.h> // <--- 1. 确保包含 OpenMP 头文件
#include <immintrin.h> // <--- 1. 包含 SIMD intrinsics 头文件

void merge_hash(const uint8_t block1[64], 
               const uint8_t block2[64],
               uint8_t output[64]) {

    uint32_t state[16] = {0};
    
    const uint32_t *w1 = (const uint32_t*)block1;
    const uint32_t *w2 = (const uint32_t*)block2;
    
    for (int i = 0; i < 8; ++i) {
        state[i]   = w1[i] ^ w2[7-i];  
        state[8+i] = w2[i] ^ w1[7-i];
    }
    
    for (int round = 0; round < 10; ++round) {
        // 展开内部的两个 i=0..3 循环

        // 原 for (int i = 0; i < 4; ++i)
        state[0]   += state[4];  state[0]   = ROTL32(state[0],   7);
        state[8]   += state[12]; state[8]   = ROTL32(state[8], 7);
        state[1]   += state[5];  state[1]   = ROTL32(state[1],   7);
        state[9]   += state[13]; state[9]   = ROTL32(state[9], 7);
        state[2]   += state[6];  state[2]   = ROTL32(state[2],   7);
        state[10]  += state[14]; state[10]  = ROTL32(state[10], 7);
        state[3]   += state[7];  state[3]   = ROTL32(state[3],   7);
        state[11]  += state[15]; state[11]  = ROTL32(state[11], 7);

        // 原 for (int i = 0; i < 4; ++i)
        state[0]   += state[8];   state[0]   = ROTL32(state[0],   9);
        state[4]   += state[12];  state[4]   = ROTL32(state[4], 9);
        state[1]   += state[9];   state[1]   = ROTL32(state[1],   9);
        state[5]   += state[13];  state[5]   = ROTL32(state[5], 9);
        state[2]   += state[10];  state[2]   = ROTL32(state[2],   9);
        state[6]   += state[14];  state[6]   = ROTL32(state[6], 9);
        state[3]   += state[11];  state[3]   = ROTL32(state[3],   9);
        state[7]   += state[15];  state[7]   = ROTL32(state[7], 9);
    }
    
    for (int i = 0; i < 8; ++i) {
        state[i] += state[15-i];
    }
    
    memcpy(output, state, 64);
}

void merkel_tree(const uint8_t *input, uint8_t *output, size_t length){ // length 是输入数据的总字节数

    uint8_t * cur_buf  = malloc(length); // 分配当前层级哈希的缓冲区
    uint8_t * prev_buf = malloc(length); // 分配上一层级哈希的缓冲区
    memcpy(prev_buf, input, length);     // 将输入数据复制到 prev_buf

    size_t current_level_processing_bytes = length; // 用于迭代的变量，表示 prev_buf 中有效的字节数
                                                    // 原始代码中使用 length 变量自身进行迭代更新

    // 循环构建哈希树，直到只剩下一个64字节的根哈希
    // 原始代码的循环条件是 length >= 64，并且在循环内部 length /= 2;
    // 为了更清晰地分离原始输入 length 和循环中变化的当前层数据大小，这里引入新变量
    while (current_level_processing_bytes > 64) { // 当 prev_buf 中的数据量大于一个哈希块时，继续合并
        size_t num_hashes_to_compute_this_level = (current_level_processing_bytes / 64) / 2;

        // 对当前层级的哈希计算应用 OpenMP 并行化
        // 每次迭代计算一对子哈希的父哈希，这些计算是相互独立的
        #pragma omp parallel for
        for (int i = 0; i < num_hashes_to_compute_this_level; ++i) {
            merge_hash(prev_buf + (2 * i) * 64,      // 从 prev_buf 中取第1个子哈希
                       prev_buf + (2 * i + 1) * 64,  // 从 prev_buf 中取第2个子哈希
                       cur_buf + i * 64);            // 将计算得到的父哈希存入 cur_buf
        }

        current_level_processing_bytes /= 2; // 下一层级（现在在 cur_buf 中）的数据总字节数减半

        // 交换 prev_buf 和 cur_buf 指针，为下一轮迭代做准备
        // cur_buf 中是刚计算完的哈希，它将成为下一轮的 "上一层" (prev_buf)
        uint8_t *tmp = cur_buf;
        cur_buf = prev_buf;
        prev_buf = tmp;
    }

    // 当循环结束时，prev_buf 中存储的就是最终的64字节根哈希
    memcpy(output, prev_buf, 64);

    // 释放分配的内存
    free(cur_buf);
    free(prev_buf);
}
